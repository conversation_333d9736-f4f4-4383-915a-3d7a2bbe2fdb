###############################################################################
#
# IAR ELF Linker V3.10.1.201 for STM8                     17/Jul/2018  23:48:57
# Copyright 2010-2017 IAR Systems AB.
#
#    Output file  =  D:\2\Debug\Exe\22.out
#    Map file     =  D:\2\Debug\List\22.map
#    Command line =  
#        -f C:\Users\<USER>\AppData\Local\Temp\EWF41F.tmp
#        (D:\2\Debug\Obj\main.o D:\2\Debug\Obj\ReadAd.o --redirect
#        _Printf=_PrintfFullNoMb --redirect _Scanf=_ScanfFullNoMb -o
#        D:\2\Debug\Exe\22.out --map D:\2\Debug\List\22.map --config
#        "D:\Program Files (x86)\IAR Systems\Embedded Workbench
#        8.0\stm8\config\lnkstm8l001j3.icf" --config_def _CSTACK_SIZE=0x100
#        --config_def _HEAP_SIZE=0x100 --entry __iar_program_start --debug_lib)
#
###############################################################################

*******************************************************************************
*** RUNTIME MODEL ATTRIBUTES
***

__SystemLibrary            = DLib
__code_model               = small
__core                     = stm8
__data_model               = medium
__dlib_file_descriptor     = 0
__dlib_full_locale_support = 0
__rt_version               = 4


*******************************************************************************
*** PLACEMENT SUMMARY
***

"A0":  place at start of [0x000000-0x0000ff] { rw section .vregs };
"A1":  place at end of [0x000000-0x0005ff] { block CSTACK };
"P2":  place in [from 0x000000 to 0x0005ff] {
          block HEAP, rw section __DLIB_PERTHREAD, rw section .far.bss,
          rw section .far.data, rw section .far.noinit,
          rw section .far_func.textrw, rw section .huge.bss,
          rw section .huge.data, rw section .huge.noinit,
          rw section .huge_func.textrw, rw section .iar.dynexit,
          rw section .near.bss, rw section .near.data, rw section .near.noinit,
          rw section .near_func.textrw };
"A2":  place at start of [0x008000-0x009fff] { block INTVEC };
"P3":  place in [from 0x008000 to 0x009fff] {
          ro section __DLIB_PERTHREAD_init, ro section .far.data_init,
          ro section .far_func.textrw_init, ro section .huge.data_init,
          ro section .huge_func.textrw_init, ro section .iar.init_table,
          ro section .init_array, ro section .near.data_init,
          ro section .near.rodata, ro section .near_func.text,
          ro section .near_func.textrw_init, ro section .tiny.data_init,
          ro section .tiny.rodata_init };
"P4":  place in [from 0x008000 to 0x009fff] {
          ro section .far.rodata, ro section .far_func.text };
"P5":  place in [from 0x008000 to 0x009fff] {
          ro section .huge.rodata, ro section .huge_func.text };
do not initialize {
   rw section .far.noinit, rw section .huge.noinit, rw section .near.noinit,
   rw section .tiny.noinit, rw section .vregs };

  Section          Kind      Address    Size  Object
  -------          ----      -------    ----  ------
"A0":                                   0x10
  .vregs           uninit   0x000000    0x10  vregs.o [4]
                          - 0x000010    0x10

"P2":                                   0x97
  .near.bss        zero     0x000010    0x70  main.o [1]
  .near.bss        zero     0x000080     0xa  main.o [1]
  .near.bss        zero     0x00008a     0x4  main.o [1]
  .near.bss        zero     0x00008e     0x4  main.o [1]
  .near.bss        zero     0x000092     0x4  main.o [1]
  .near.bss        zero     0x000096     0x4  main.o [1]
  .near.bss        zero     0x00009a     0x2  main.o [1]
  .near.bss        zero     0x00009c     0x2  main.o [1]
  .near.bss        zero     0x00009e     0x2  main.o [1]
  .near.bss        zero     0x0000a0     0x2  main.o [1]
  .near.bss        zero     0x0000a2     0x2  main.o [1]
  .near.bss        zero     0x0000a4     0x1  main.o [1]
  .near.bss        zero     0x0000a5     0x1  main.o [1]
  .near.bss        zero     0x0000a6     0x1  main.o [1]
                          - 0x0000a7    0x97

"A1":                                  0x100
  CSTACK                    0x000500   0x100  <Block>
    CSTACK         uninit   0x000500   0x100  <Block tail>
                          - 0x000600   0x100

Absolute sections, part 1 of 10:         0x1
  .near.noinit     uninit   0x005000     0x1  main.o [1]
                          - 0x005001     0x1

Absolute sections, part 2 of 10:         0x4
  .near.noinit     uninit   0x005002     0x1  main.o [1]
  .near.noinit     uninit   0x005003     0x1  main.o [1]
  .near.noinit     uninit   0x005004     0x1  main.o [1]
  .near.noinit     uninit   0x005005     0x1  main.o [1]
                          - 0x005006     0x4

Absolute sections, part 3 of 10:         0x9
  .near.noinit     uninit   0x005007     0x1  main.o [1]
  .near.noinit     uninit   0x005008     0x1  main.o [1]
  .near.noinit     uninit   0x005009     0x1  main.o [1]
  .near.noinit     uninit   0x00500a     0x1  ReadAd.o [1]
  .near.noinit     uninit   0x00500b     0x1  ReadAd.o [1]
  .near.noinit     uninit   0x00500c     0x1  ReadAd.o [1]
  .near.noinit     uninit   0x00500d     0x1  main.o [1]
  .near.noinit     uninit   0x00500e     0x1  main.o [1]
  .near.noinit     uninit   0x00500f     0x1  main.o [1]
                          - 0x005010     0x9

Absolute sections, part 4 of 10:         0x3
  .near.noinit     uninit   0x005011     0x1  main.o [1]
  .near.noinit     uninit   0x005012     0x1  main.o [1]
  .near.noinit     uninit   0x005013     0x1  main.o [1]
                          - 0x005014     0x3

Absolute sections, part 5 of 10:         0x1
  .near.noinit     uninit   0x0050c0     0x1  main.o [1]
                          - 0x0050c1     0x1

Absolute sections, part 6 of 10:         0x1
  .near.noinit     uninit   0x0050c3     0x1  main.o [1]
                          - 0x0050c4     0x1

Absolute sections, part 7 of 10:         0x2
  .near.noinit     uninit   0x0050e0     0x1  main.o [1]
  .near.noinit     uninit   0x0050e1     0x1  main.o [1]
                          - 0x0050e2     0x2

Absolute sections, part 8 of 10:         0x1
  .near.noinit     uninit   0x0052e0     0x1  main.o [1]
                          - 0x0052e1     0x1

Absolute sections, part 9 of 10:         0x1
  .near.noinit     uninit   0x0052e4     0x1  main.o [1]
                          - 0x0052e5     0x1

Absolute sections, part 10 of 10:        0x2
  .near.noinit     uninit   0x0052e7     0x1  main.o [1]
  .near.noinit     uninit   0x0052e8     0x1  main.o [1]
                          - 0x0052e9     0x2

"A2":                                   0x80
  INTVEC                    0x008000    0x80  <Block>
    .intvec        const    0x008000    0x80  interrupt.o [4]
                          - 0x008080    0x80

"P3-P5":                              0x1262
  .near_func.text  ro code  0x008080   0x2f3  float.o [4]
  .near_func.text  ro code  0x008373   0x1d6  long.o [4]
  .near.rodata     const    0x008549   0x1aa  main.o [1]
  .near_func.text  ro code  0x0086f3   0x171  main.o [1]
  .near_func.text  ro code  0x008864   0x15e  main.o [1]
  .near_func.text  ro code  0x0089c2   0x13d  ReadAd.o [1]
  .near_func.text  ro code  0x008aff    0xe9  vreg_util.o [4]
  .near_func.text  ro code  0x008be8    0xe7  main.o [1]
  .near_func.text  ro code  0x008ccf    0xdd  main.o [1]
  .near_func.text  ro code  0x008dac    0xc4  main.o [1]
  .near_func.text  ro code  0x008e70    0xae  main.o [1]
  .near_func.text  ro code  0x008f1e    0xac  ReadAd.o [1]
  .near_func.text  ro code  0x008fca    0x7d  long_util.o [4]
  .near_func.text  ro code  0x009047    0x55  main.o [1]
  .near_func.text  ro code  0x00909c    0x55  ReadAd.o [1]
  .near_func.text  ro code  0x0090f1    0x51  main.o [1]
  .near_func.text  ro code  0x009142    0x51  main.o [1]
  .near_func.text  ro code  0x009193    0x23  main.o [1]
  .iar.init_table  const    0x0091b6     0x8  - Linker created -
  .near_func.text  ro code  0x0091be    0x1e  short.o [4]
  .near_func.text  ro code  0x0091dc    0x1e  init_small.o [4]
  .near_func.text  ro code  0x0091fa    0x1b  main.o [1]
  .near_func.text  ro code  0x009215    0x18  dc_util.o [4]
  .near_func.text  ro code  0x00922d    0x17  main.o [1]
  .near_func.text  ro code  0x009244    0x15  main.o [1]
  .near_func.text  ro code  0x009259    0x14  init.o [4]
  .near_func.text  ro code  0x00926d    0x14  __dbg_xxexit.o [3]
  .near_func.text  ro code  0x009281    0x13  cstartup.o [4]
  .near_func.text  ro code  0x009294    0x11  main.o [1]
  .near_func.text  ro code  0x0092a5     0xb  ReadAd.o [1]
  .near_func.text  ro code  0x0092b0     0xb  ReadAd.o [1]
  .near_func.text  ro code  0x0092bb     0xb  ReadAd.o [1]
  .near_func.text  ro code  0x0092c6     0x5  main.o [1]
  .near_func.text  ro code  0x0092cb     0x5  main.o [1]
  .near_func.text  ro code  0x0092d0     0x5  cexit.o [4]
  .near_func.text  ro code  0x0092d5     0x3  interrupt.o [4]
  .near_func.text  ro code  0x0092d8     0x3  low_level_init.o [4]
  .near_func.text  ro code  0x0092db     0x3  exit.o [4]
  .near_func.text  ro code  0x0092de     0x3  unhandled_exception.o [4]
  .near_func.text  ro code  0x0092e1     0x1  __dbg_break.o [3]
                          - 0x0092e2  0x1262


*******************************************************************************
*** INIT TABLE
***

          Address   Size
          -------   ----
Zero (__iar_zero_init2)
    1 destination range, total size 0x97:
          0x000010  0x97



*******************************************************************************
*** MODULE SUMMARY
***

    Module                 ro code  ro data  rw data  rw data
                                                       (abs)
    ------                 -------  -------  -------  -------
D:\2\Debug\Obj: [1]
    ReadAd.o                   607                          3
    main.o                   1 921      426      151       22
    ---------------------------------------------------------
    Total:                   2 528      426      151       25

command line: [2]
    ---------------------------------------------------------
    Total:

dbgstm8smd.a: [3]
    __dbg_break.o                1
    __dbg_xxexit.o              20
    ---------------------------------------------------------
    Total:                      21

dlstm8smn.a: [4]
    cexit.o                      5
    cstartup.o                  19
    dc_util.o                   24
    exit.o                       3
    float.o                    755
    init.o                      20
    init_small.o                30
    interrupt.o                  3      128
    long.o                     470
    long_util.o                125
    low_level_init.o             3
    short.o                     30
    unhandled_exception.o        3
    vreg_util.o                233
    vregs.o                                       16
    ---------------------------------------------------------
    Total:                   1 723      128       16

    Linker created                        8      256
-------------------------------------------------------------
    Grand Total:             4 272      562      423       25


*******************************************************************************
*** ENTRY LIST
***

Entry                    Address   Size  Type      Object
-----                    -------   ----  ----      ------
.iar.init_table$$Base   0x0091b6          --   Gb  - Linker created -
.iar.init_table$$Limit  0x0091be          --   Gb  - Linker created -
?add32_l0_l0_0x         0x0083a3         Code  Gb  long.o [4]
?add32_l0_l0_dl         0x00839b         Code  Gb  long.o [4]
?add32_l0_l0_l1         0x0083a0         Code  Gb  long.o [4]
?b0                     0x000000         Data  Gb  vregs.o [4]
?b1                     0x000001         Data  Gb  vregs.o [4]
?b10                    0x00000a         Data  Gb  vregs.o [4]
?b11                    0x00000b         Data  Gb  vregs.o [4]
?b12                    0x00000c         Data  Gb  vregs.o [4]
?b13                    0x00000d         Data  Gb  vregs.o [4]
?b14                    0x00000e         Data  Gb  vregs.o [4]
?b15                    0x00000f         Data  Gb  vregs.o [4]
?b2                     0x000002         Data  Gb  vregs.o [4]
?b3                     0x000003         Data  Gb  vregs.o [4]
?b4                     0x000004         Data  Gb  vregs.o [4]
?b5                     0x000005         Data  Gb  vregs.o [4]
?b6                     0x000006         Data  Gb  vregs.o [4]
?b7                     0x000007         Data  Gb  vregs.o [4]
?b8                     0x000008         Data  Gb  vregs.o [4]
?b9                     0x000009         Data  Gb  vregs.o [4]
?dc32_l1                0x009215         Code  Gb  dc_util.o [4]
?dc32_px                0x009222         Code  Gb  dc_util.o [4]
?e0                     0x000001         Data  Gb  vregs.o [4]
?e1                     0x000005         Data  Gb  vregs.o [4]
?e2                     0x000009         Data  Gb  vregs.o [4]
?e3                     0x00000d         Data  Gb  vregs.o [4]
?epilogue_l2            0x008b6f         Code  Gb  vreg_util.o [4]
?epilogue_l2_l3         0x008b6c         Code  Gb  vreg_util.o [4]
?fcastf32s32_l0_l0      0x00822f         Code  Gb  float.o [4]
?fcasts32f32_l0_l0      0x0081ce         Code  Gb  float.o [4]
?fdiv32_l0_l0_l1        0x008120         Code  Gb  float.o [4]
?fmul32_l0_l0_dc32      0x008080         Code  Gb  float.o [4]
?fmul32_l0_l0_l1        0x008085         Code  Gb  float.o [4]
?inc32_0x_0x            0x00838d         Code  Gb  long.o [4]
?inc32_l0_l0            0x008383         Code  Gb  long.o [4]
?inc32_l3_l3            0x008388         Code  Gb  long.o [4]
?l0                     0x000000         Data  Gb  vregs.o [4]
?l1                     0x000004         Data  Gb  vregs.o [4]
?l2                     0x000008         Data  Gb  vregs.o [4]
?l3                     0x00000c         Data  Gb  vregs.o [4]
?load32_0x_l0           0x00902b         Code  Gb  long_util.o [4]
?load32_0x_l2           0x009039         Code  Gb  long_util.o [4]
?load32_asp_l0          0x00901f         Code  Gb  long_util.o [4]
?load32_dbsp_l0         0x00901a         Code  Gb  long_util.o [4]
?load32_l0_0x           0x008fda         Code  Gb  long_util.o [4]
?load32_l0_asp          0x008fcf         Code  Gb  long_util.o [4]
?load32_l0_dbsp         0x008fca         Code  Gb  long_util.o [4]
?load32_l0_xsp          0x008fd1         Code  Gb  long_util.o [4]
?load32_l1_0x           0x008ff6         Code  Gb  long_util.o [4]
?load32_l1_asp          0x008feb         Code  Gb  long_util.o [4]
?load32_l1_dbsp         0x008fe6         Code  Gb  long_util.o [4]
?load32_l1_xsp          0x008fed         Code  Gb  long_util.o [4]
?load32_l2_0x           0x009002         Code  Gb  long_util.o [4]
?load32_l3_0x           0x00900e         Code  Gb  long_util.o [4]
?load32_xsp_l0          0x009021         Code  Gb  long_util.o [4]
?mov_e0_e1              0x008b76         Code  Gb  vreg_util.o [4]
?mov_e0_e2              0x008b83         Code  Gb  vreg_util.o [4]
?mov_e0_e3              0x008b90         Code  Gb  vreg_util.o [4]
?mov_e1_e0              0x008b9d         Code  Gb  vreg_util.o [4]
?mov_e1_e2              0x008baa         Code  Gb  vreg_util.o [4]
?mov_e1_e3              0x008bb7         Code  Gb  vreg_util.o [4]
?mov_e2_e0              0x008bc4         Code  Gb  vreg_util.o [4]
?mov_e3_e0              0x008bd1         Code  Gb  vreg_util.o [4]
?mov_e3_e1              0x008bde         Code  Gb  vreg_util.o [4]
?mov_l0_l1              0x008b73         Code  Gb  vreg_util.o [4]
?mov_l0_l2              0x008b80         Code  Gb  vreg_util.o [4]
?mov_l0_l3              0x008b8d         Code  Gb  vreg_util.o [4]
?mov_l1_l0              0x008b9a         Code  Gb  vreg_util.o [4]
?mov_l1_l2              0x008ba7         Code  Gb  vreg_util.o [4]
?mov_l1_l3              0x008bb4         Code  Gb  vreg_util.o [4]
?mov_l2_l0              0x008bc1         Code  Gb  vreg_util.o [4]
?mov_l3_l0              0x008bce         Code  Gb  vreg_util.o [4]
?mov_l3_l1              0x008bdb         Code  Gb  vreg_util.o [4]
?mov_w1_w3              0x008b79         Code  Gb  vreg_util.o [4]
?mov_w1_w5              0x008b86         Code  Gb  vreg_util.o [4]
?mov_w1_w7              0x008b93         Code  Gb  vreg_util.o [4]
?mov_w3_w1              0x008ba0         Code  Gb  vreg_util.o [4]
?mov_w3_w5              0x008bad         Code  Gb  vreg_util.o [4]
?mov_w3_w7              0x008bba         Code  Gb  vreg_util.o [4]
?mov_w5_w1              0x008bc7         Code  Gb  vreg_util.o [4]
?mov_w7_w1              0x008bd4         Code  Gb  vreg_util.o [4]
?mov_w7_w3              0x008be1         Code  Gb  vreg_util.o [4]
?mul16_x_x_w0           0x0091be         Code  Gb  short.o [4]
?mul32_l0_l0_0x         0x0083d9         Code  Gb  long.o [4]
?mul32_l0_l0_dl         0x0083d6         Code  Gb  long.o [4]
?mul32_l0_l0_l1         0x0083e2         Code  Gb  long.o [4]
?neg32_l0_l0            0x008373         Code  Gb  long.o [4]
?pop_l1                 0x008b43         Code  Gb  vreg_util.o [4]
?pop_l2                 0x008b4e         Code  Gb  vreg_util.o [4]
?pop_l3                 0x008b59         Code  Gb  vreg_util.o [4]
?push_l0                0x008b17         Code  Gb  vreg_util.o [4]
?push_l2                0x008b1c         Code  Gb  vreg_util.o [4]
?push_l3                0x008b21         Code  Gb  vreg_util.o [4]
?push_w4                0x008aff         Code  Gb  vreg_util.o [4]
?scmp32_c_l0_l1         0x008516         Code  Gb  long.o [4]
?sdiv32_l0_l0_dl        0x0084cd         Code  Gb  long.o [4]
?sdiv32_l0_l0_l1        0x0084d0         Code  Gb  long.o [4]
?sext32_l0_x            0x00853e         Code  Gb  long.o [4]
?sll32_0x_0x_a          0x008530         Code  Gb  long.o [4]
?sll32_l0_l0_a          0x00852d         Code  Gb  long.o [4]
?smod32_l1_l0_l1        0x0084d0         Code  Gb  long.o [4]
?sub32_l0_l0_0x         0x0083be         Code  Gb  long.o [4]
?sub32_l0_l0_l1         0x0083bb         Code  Gb  long.o [4]
?udiv32_l0_l0_l1        0x008460         Code  Gb  long.o [4]
?umod32_l1_l0_l1        0x008460         Code  Gb  long.o [4]
?w0                     0x000000         Data  Gb  vregs.o [4]
?w1                     0x000002         Data  Gb  vregs.o [4]
?w2                     0x000004         Data  Gb  vregs.o [4]
?w3                     0x000006         Data  Gb  vregs.o [4]
?w4                     0x000008         Data  Gb  vregs.o [4]
?w5                     0x00000a         Data  Gb  vregs.o [4]
?w6                     0x00000c         Data  Gb  vregs.o [4]
?w7                     0x00000e         Data  Gb  vregs.o [4]
AdDataPro               0x0086f3  0x171  Code  Gb  main.o [1]
AdPga                   0x0000a5    0x1  Data  Gb  main.o [1]
AlarmValue_Open         0x00009a    0x2  Data  Gb  main.o [1]
AlarmValue_OverRang     0x00009e    0x2  Data  Gb  main.o [1]
AlarmValue_Short        0x00009c    0x2  Data  Gb  main.o [1]
CLK_PCKENR              0x0050c3    0x1  Data  Gb  main.o [1]
CSTACK$$Base            0x000500          --   Gb  - Linker created -
CSTACK$$Limit           0x000600          --   Gb  - Linker created -
Ch                      0x000010   0x70  Data  Gb  main.o [1]
Default_Varia           0x009047   0x55  Code  Gb  main.o [1]
Delay                   0x00922d   0x17  Code  Gb  main.o [1]
I2C_GetAck              0x008acb   0x1c  Code  Gb  ReadAd.o [1]
I2C_RxByte              0x008a86   0x45  Code  Gb  ReadAd.o [1]
I2C_SCLH                0x0092bb    0xb  Code  Gb  ReadAd.o [1]
I2C_SCLL                0x0089c2    0xb  Code  Gb  ReadAd.o [1]
I2C_SDAH                0x0092a5    0xb  Code  Gb  ReadAd.o [1]
I2C_SDAL                0x0092b0    0xb  Code  Gb  ReadAd.o [1]
I2C_START               0x0089cd   0x2d  Code  Gb  ReadAd.o [1]
I2C_STOP                0x0089fa   0x47  Code  Gb  ReadAd.o [1]
I2C_SetACK              0x008ae7    0xc  Code  Gb  ReadAd.o [1]
I2C_SetNAK              0x008af3    0xc  Code  Gb  ReadAd.o [1]
I2C_TxByte              0x008a41   0x45  Code  Gb  ReadAd.o [1]
INTVEC$$Base            0x008000          --   Gb  - Linker created -
INTVEC$$Limit           0x008080          --   Gb  - Linker created -
IWDG_KR                 0x0050e0    0x1  Data  Gb  main.o [1]
InSignalDis             0x008ccf   0xdd  Code  Gb  main.o [1]
InTempValue             0x00008a    0x4  Data  Gb  main.o [1]
Init_Ad                 0x0092cb    0x5  Code  Gb  main.o [1]
Init_Clock              0x0092c6    0x5  Code  Gb  main.o [1]
Init_IWDG               0x009294   0x11  Code  Gb  main.o [1]
Init_Io                 0x0090f1   0x51  Code  Gb  main.o [1]
Init_TIM4               0x009244   0x15  Code  Gb  main.o [1]
Init_Task               0x009193   0x23  Code  Gb  main.o [1]
MessageFlag             0x0000a2    0x2  Data  Gb  main.o [1]
Protection              0x0000a4    0x1  Data  Gb  main.o [1]
ReadAdData              0x00008e    0x4  Data  Gb  main.o [1]
ReadAd_task             0x008be8   0xe7  Code  Gb  main.o [1]
ReadMc3421              0x008f1e   0xac  Code  Gb  ReadAd.o [1]
Region$$Table$$Base     0x0091b6          --   Gb  - Linker created -
Region$$Table$$Limit    0x0091be          --   Gb  - Linker created -
SetGain                 0x0090be   0x33  Code  Gb  ReadAd.o [1]
Set_Timer               0x0091fa   0x1b  Code  Gb  main.o [1]
Step_Ad                 0x0000a6    0x1  Data  Gb  main.o [1]
SumFilt                 0x008e70   0xae  Code  Gb  main.o [1]
TIM4_ARR                0x0052e8    0x1  Data  Gb  main.o [1]
Table_PT100             0x008549  0x1aa  Data  Gb  main.o [1]
TempResistancePro       0x008864  0x15e  Code  Gb  main.o [1]
TimerCount              0x0000a0    0x2  Data  Gb  main.o [1]
UpdateTimer             0x009142   0x51  Code  Gb  main.o [1]
WriteMc3421             0x00909c   0x22  Code  Gb  ReadAd.o [1]
_A_CLK_CKDIVR           0x0050c0    0x1  Data  Gb  main.o [1]
_A_IWDG_PR              0x0050e1    0x1  Data  Gb  main.o [1]
_A_PA_CR1               0x005003    0x1  Data  Gb  main.o [1]
_A_PA_CR2               0x005004    0x1  Data  Gb  main.o [1]
_A_PA_DDR               0x005002    0x1  Data  Gb  main.o [1]
_A_PA_ODR               0x005000    0x1  Data  Gb  main.o [1]
_A_PB_CR1               0x005008    0x1  Data  Gb  main.o [1]
_A_PB_CR2               0x005009    0x1  Data  Gb  main.o [1]
_A_PB_DDR               0x005007    0x1  Data  Gb  main.o [1]
_A_PB_ODR               0x005005    0x1  Data  Gb  main.o [1]
_A_PC_CR1               0x00500d    0x1  Data  Gb  main.o [1]
_A_PC_CR2               0x00500e    0x1  Data  Gb  main.o [1]
_A_PC_DDR               0x00500c    0x1  Data  Gb  ReadAd.o [1]
_A_PC_IDR               0x00500b    0x1  Data  Gb  ReadAd.o [1]
_A_PC_ODR               0x00500a    0x1  Data  Gb  ReadAd.o [1]
_A_PD_CR1               0x005012    0x1  Data  Gb  main.o [1]
_A_PD_CR2               0x005013    0x1  Data  Gb  main.o [1]
_A_PD_DDR               0x005011    0x1  Data  Gb  main.o [1]
_A_PD_ODR               0x00500f    0x1  Data  Gb  main.o [1]
_A_TIM4_CR1             0x0052e0    0x1  Data  Gb  main.o [1]
_A_TIM4_PSCR            0x0052e7    0x1  Data  Gb  main.o [1]
_A_TIM4_SR1             0x0052e4    0x1  Data  Gb  main.o [1]
__DebugBreak            0x0092e1    0x1  Code  Gb  __dbg_break.o [3]
__exit                  0x00926d   0x14  Code  Gb  __dbg_xxexit.o [3]
__iar_data_init2        0x009259   0x14  Code  Gb  init.o [4]
__iar_program_start     0x009281         Code  Gb  cstartup.o [4]
__iar_unhandled_exception
                        0x0092de         Code  Gb  unhandled_exception.o [4]
__iar_zero_init2        0x0091dc         Code  Gb  init_small.o [4]
__intvec                0x008000         Data  Gb  interrupt.o [4]
__low_level_init        0x0092d8    0x3  Code  Gb  low_level_init.o [4]
_exit                   0x0092d0         Code  Gb  cexit.o [4]
exit                    0x0092db    0x3  Code  Gb  exit.o [4]
ii                      0x000096    0x4  Data  Gb  main.o [1]
iii                     0x000092    0x4  Data  Gb  main.o [1]
main                    0x008dac   0xc4  Code  Gb  main.o [1]
test                    0x000080    0xa  Data  Gb  main.o [1]


[1] = D:\2\Debug\Obj
[2] = command line
[3] = dbgstm8smd.a
[4] = dlstm8smn.a

  4 272 bytes of readonly  code memory
    562 bytes of readonly  data memory
    423 bytes of readwrite data memory (+ 25 absolute)

Errors: none
Warnings: none
