/*

2018.11.05————————改为4-20mA输入，AD值15次滤波，取中间4次。消抖极值，满足VP8电流特性。

2019.05.10————————1、更改CPU，改为STM8S003F3
                  2、双通道mA，二入二出
                  3、ADC采用CS1237

2019.06.04————————1、增加一入四出
                  2、改为一点校准，开机自校零点。
*/

#include    "iostm8s003f3.h"
#include    "Define.h"
#include    "hVaria.h"
#include    "hCs1237.h"
#include    "hPwm.h"
#include    "hEeprom.h"
#include    "hUart.h"

unsigned char test[10];

//Delay(1)------0.2ms延时，4MIPS
//Delay(1)------0.1ms延时，8MIPS

void Delay(uint i)
{
  uchar j;
  uint  k;

#ifdef fMASTER_16M
  for(k=0; k<i; k++ )
  {
    for(j=0; j<200; j++)
    {nop;nop;nop;nop;}
  }
#endif  

#ifdef fMASTER_8M
  for(k=0; k<i; k++ )
  {
    for(j=0; j<100; j++)
    {nop;nop;nop;nop;}
  }
#endif  

#ifdef fMASTER_4M
  for(k=0; k<i; k++ )
  {
    for(j=0; j<50; j++)
    {nop;nop;nop;nop;}
  }
#endif  

}

//缺省变量
void Default_Varia(void)
{
 Protection               = 0x5a;        ;
 AlarmValue_Open          = 3500         ;
 AlarmValue_Short         = 3500         ;
 AlarmValue_OverRang      = 21000        ;

 Ch[ 0 ].SignalInputType     = IN_4_20mA    ;
 Ch[ 0 ].SignalOutputType    = OUT_4_20mA   ;
 Ch[ 0 ].InputCurrent        = 46071        ;
 Ch[ 0 ].InputCurrent_Zero   = 9200         ;
 Ch[ 0 ].OutCurrentH         = 19814        ;   
 Ch[ 0 ].OutCurrentL         = 3860         ;      
 Ch[ 1 ].SignalInputType     = IN_4_20mA    ;
 Ch[ 1 ].SignalOutputType    = OUT_4_20mA   ;
 Ch[ 1 ].InputCurrent        = 46071        ;
 Ch[ 1 ].InputCurrent_Zero   = 9200         ;
 Ch[ 1 ].OutCurrentH         = 19814        ;   
 Ch[ 1 ].OutCurrentL         = 3860         ;  
 Ch34[ 0 ].OutCurrentH       = 19814        ;   
 Ch34[ 0 ].OutCurrentL       = 3860         ; 
 Ch34[ 1 ].OutCurrentH       = 19814        ;   
 Ch34[ 1 ].OutCurrentL       = 3860         ; 
}

//初始化FLASH，读参数
void Init_Flash(void)
{

	uchar i;
	uchar Buf[5];
	
  //Init_Eeprom();
  
  for(i=0;i<5;i++)
	{
		//先读5次标志位
		Delay(10);
		ReadData( Addr_Protection );
		Buf[i] = Protection;
		nop;
		nop;
	}

	if( (Buf[0] == 0x5a )&&(Buf[1] == 0x5a )&&(Buf[2] == 0x5a )&&(Buf[3] == 0x5a )&&(Buf[4] == 0x5a ) )
		{
			ReadAllData();
		}
	else
		{
			for(i=0;i<5;i++)
			{
				Default_Varia();  //缺省值
				WriteAllData();
				
				//读5次
				Delay(100);
				ReadData( Addr_Protection );
				nop;
				nop;
				if(Protection == 0x5a)
					break;
				else
					while(1);
			}
		}

}

//硬件看门狗--------255ms
/*
STM8S003F3
IWDG_PR_DIV_32------127ms
IWDG_PR_DIV_64------255ms
IWDG_PR_DIV_128-----510ms

STM8L001J3
IWDG_PR_DIV_16------107ms
IWDG_PR_DIV_32------215ms
IWDG_PR_DIV_64------431ms
*/
void Init_IWDG(void)
{
  IWDG_KR  = 0xcc;              //启用wdg，注意需要先启动使能再写分频值才有效，2019.05.12
  
  IWDG_KR  = 0x55;              //解锁IWDG_PR IWDG_RLR
  IWDG_PR  = IWDG_PR_DIV_128;   //T = (1/fLSI)*128**0XFF = 510ms(128kHz)
  //IWDG_RLR = 0xff;
  
  IWDG_KR  = 0xaa;              //锁定并刷新
}

//16MHz-------------2.70mA
//8MHz--------------1.50mA
//4MHz--------------0.88mA
//2MHz--------------0.55mA
void Init_Clock(void)
{
	//开内部LRC时钟
	CLK_ICKR_LSIEN = 1;
	nop;
	nop;
	
	//等待内部LSI(RC)准备好
	while(CLK_ICKR_LSIRDY==0)
	{
		nop;
	}

	//开内部HRC时钟
	CLK_ICKR_HSIEN = 1;
	nop;
	nop;
		
	//等待内部HSI(RC)准备好
	while(CLK_ICKR_HSIRDY==0)
	{
		nop;
	}
	
	//设置HSI为主时钟(0xE1---HSI; 0xD2---LSI; 0xB4---HSE)
	CLK_SWR = 0xE1;
	
//--------------------------------------------------------------------------------
#ifdef  fMASTER_16M

  CLK_CKDIVR_HSIDIV = CLK_CKDIVR_HSIDIV_0;  //fMaster = 16MHz
  CLK_CKDIVR_CPUDIV = CLK_CKDIVR_CPUDIV_0;  //fCpu    = 16MHz

#endif

#ifdef   fMASTER_8M

  CLK_CKDIVR_HSIDIV = CLK_CKDIVR_HSIDIV_2;  //fMaster = 8MHz
  CLK_CKDIVR_CPUDIV = CLK_CKDIVR_CPUDIV_0;  //fCpu    = 8MHz

#endif

#ifdef   fMASTER_4M

  CLK_CKDIVR_HSIDIV = CLK_CKDIVR_HSIDIV_4;  //fMaster = 4MHz
  CLK_CKDIVR_CPUDIV = CLK_CKDIVR_CPUDIV_0;  //fCpu    = 4MHz

#endif

#ifdef   fMASTER_2M

  CLK_CKDIVR_HSIDIV = CLK_CKDIVR_HSIDIV_8;  //fMaster = 2MHz
  CLK_CKDIVR_CPUDIV = CLK_CKDIVR_CPUDIV_0;  //fCpu    = 2MHz

#endif
//-------------------------------------------------------------------------------- 
	
	//外设时钟全关，用哪个在外设初始化中开哪个
	CLK_PCKENR1 = 0;
  CLK_PCKENR2 = 0;
}

void Init_Option(void)
{
	
}

/*
PC_DDR_DDR0 = 1;  //0 is 输入模式                    ; 1 is 输出模式
PC_CR1_C10  = 1;  //0 is 输入浮置   /输出开漏        ; 1 is 输入上拉   / 输出推挽
PC_CR2_C20  = 1;  //0 is 输入中断关 /输出2MHz模式    ; 1 is 输入中断开 / 输出10MHz模式 (注意其和IO中断使能复用)
PC_ODR_ODR0 = 1;  //0 is 输出高电平                  ; 1 is 输出低电平  
*/
void Init_Io(void)
{
  //PD6 = RXD
  PD_DDR_DDR6 = 0;  //输入
  PD_CR1_C16  = 1;  //输入上拉75k
  //PD_ODR_ODR6 = 1;  //输出1

  //PD5 = TXD
  PD_DDR_DDR5 = 1;  //输出
  PD_CR1_C15  = 1;  //输出推挽
  //PD_ODR_ODR6 = 1;  //输出1

  //PD3 = SelA
  PD_DDR_DDR3 = 1;  //输出
  PD_CR1_C13  = 1;  //输出推挽
  PD_ODR_ODR3 = 1;  //输出1

  //PB4 = ALM1
  PB_DDR_DDR4 = 1;  //输出
  PB_CR1_C14  = 1;  //输出推挽
  PB_ODR_ODR4 = 1;  //输出1

  //PB5 = ALM2
  PB_DDR_DDR5 = 1;  //输出
  PB_CR1_C15  = 1;  //输出推挽
  PB_ODR_ODR5 = 1;  //输出1

  //PA2 = SCL
  PA_DDR_DDR2 = 1;  //输出
  PA_CR1_C12  = 1;  //输出推挽

  //PA1 = SDA
  PA_DDR_DDR1 = 0;  //输入
  PA_CR1_C11  = 1;  //输出推挽
  //PA_ODR_ODR1 = 1;  //输出1
  
  //PC7 = PWM1
  PC_DDR_DDR7 = 1;  //输出
  PC_CR1_C17  = 1;  //输出推挽
  PC_ODR_ODR7 = 1;  //输出1

  //PC6 = PWM2
  PC_DDR_DDR6 = 1;  //输出
  PC_CR1_C16  = 1;  //输出推挽
  PC_ODR_ODR6 = 1;  //输出1

  //PC3 = PWM3
  PC_DDR_DDR3 = 1;  //输出
  PC_CR1_C13  = 1;  //输出推挽
  PC_ODR_ODR3 = 1;  //输出1

  //PC4 = PWM4
  PC_DDR_DDR4 = 1;  //输出
  PC_CR1_C14  = 1;  //输出推挽
  PC_ODR_ODR4 = 1;  //输出1

  //PA3 = test
  PA_DDR_DDR3 = 1;  //输出
  PA_CR1_C13  = 1;  //输出推挽
  PA_ODR_ODR3 = 1;  //输出1
  
  //PA3 PD4 PC5等未连接的引脚复位后默认配置为输入浮置，软件需设置为输入上拉
  PA_CR1_C13  = 1;
  PD_CR1_C14  = 1;
  PC_CR1_C15  = 1;
  
  DIR_SDA;
  OUT_SCL_H;
/*  
  PB_CR1_C10  = 1;
  PB_CR1_C11  = 1;
  PB_CR1_C12  = 1;
  PB_CR1_C14  = 1;
  
  PC_CR1_C14  = 1;
  PC_CR1_C15  = 1;
  PC_CR1_C16  = 1;

  PD_CR1_C11  = 1;
  PD_CR1_C12  = 1;
  PD_CR1_C13  = 1;
  PD_CR1_C14  = 1;
  PD_CR1_C15  = 1;
  PD_CR1_C16  = 1;
  PD_CR1_C17  = 1;*/
}

void Init_TIM1(void)
{
  CLK_PCKENR1 |= CLK_PCKEN1_TIM1;      //TIM1时钟使能
  nop;
  nop;
  nop;
  nop;
  
  TIM1_PSCRH  = 0;                      //预分频1
  TIM1_PSCRL  = 0;
  
  TIM1_ARRH  = PWM_DUTY>>8;             //频率
  TIM1_ARRL  = (uchar)PWM_DUTY;

//------------------------------------------------------------------------------PWM1    
  TIM1_CCR1H = 0;//PWM_DUTY_DIV2>>8;;          //周期 初始50%
  TIM1_CCR1L = 0;//(uchar)PWM_DUTY_DIV2;  
  
  TIM1_CCMR1_OC1M  = 6;           //输出比较模式：PWM模式1
  TIM1_CCMR1_OC1PE = 1;           //输出比较预装使能
  TIM1_CCMR1_CC1S  = 0;           //通道方向选择：输出

  TIM1_CCER1_CC1P = 1;            //极性选择，0----高电平，1----低电平
  TIM1_CCER1_CC1E = 1;            //通道1输出使能
//------------------------------------------------------------------------------PWM2  
  TIM1_CCR2H = 0;//PWM_DUTY_DIV2>>8;;          //周期 初始50%
  TIM1_CCR2L = 0;//(uchar)PWM_DUTY_DIV2;  
  
  TIM1_CCMR2_OC2M  = 6;           //输出比较模式：PWM模式1
  TIM1_CCMR2_OC2PE = 1;           //输出比较预装使能
  TIM1_CCMR2_CC2S  = 0;           //通道方向选择：输出

  TIM1_CCER1_CC2P = 1;            //极性选择，0----高电平，1----低电平
  TIM1_CCER1_CC2E = 1;            //通道1输出使能

//------------------------------------------------------------------------------PWM3 
  TIM1_CCR3H = 0;//PWM_DUTY_DIV2>>8;;          //周期 初始50%
  TIM1_CCR3L = 0;//(uchar)PWM_DUTY_DIV2;  
  
  TIM1_CCMR3_OC3M  = 6;           //输出比较模式：PWM模式1
  TIM1_CCMR3_OC3PE = 1;           //输出比较预装使能
  TIM1_CCMR3_CC3S  = 0;           //通道方向选择：输出

  TIM1_CCER2_CC3P = 1;            //极性选择，0----高电平，1----低电平
  TIM1_CCER2_CC3E = 1;            //通道1输出使能  
//------------------------------------------------------------------------------PWM4  
  TIM1_CCR4H = 0;//PWM_DUTY_DIV2>>8;;          //周期 初始50%
  TIM1_CCR4L = 0;//(uchar)PWM_DUTY_DIV2;  
  
  TIM1_CCMR4_OC4M  = 6;           //输出比较模式：PWM模式1
  TIM1_CCMR4_OC4PE = 1;           //输出比较预装使能
  TIM1_CCMR4_CC4S  = 0;           //通道方向选择：输出

  TIM1_CCER2_CC4P = 1;            //极性选择，0----高电平，1----低电平
  TIM1_CCER2_CC4E = 1;            //通道1输出使能
//------------------------------------------------------------------------------  


  TIM1_BKR_MOE    = 1;            //输出总使能
  
  TIM1_IER     = 0;               //中断关闭
  
  TIM1_CR1_CEN = 1;               //使能计数器
  
}

//T = 8.192ms  16 bit timer TIM2
void Init_TIM2(void)
{
  CLK_PCKENR1 |= CLK_PCKEN1_TIM2; //TIM2时钟使能
  nop;
  nop;
  nop;
  nop;
  
#ifdef  fMASTER_16M

  TIM2_PSCR = 12;                 //0.256ms, 预分频=1/4096    1/2^12 = 3906.25M

#endif

#ifdef   fMASTER_8M

  TIM2_PSCR = 11;                 //0.256ms, 预分频=1/2048    1/2^11 = 3906.25M

#endif

#ifdef   fMASTER_4M

  TIM2_PSCR = 10;                 //0.256ms, 预分频=1/1024    1/2^10 = 3906.25M

#endif

#ifdef   fMASTER_2M

  TIM2_PSCR = 9;                  //0.256ms, 预分频=1/512     1/2^9 = 3906.25M

#endif


  TIM2_ARRH  = 0;                //0.256ms * 32
  TIM2_ARRL  = 32;
  
  TIM2_SR1_UIF  = 0;             //清标志
  
  TIM2_CR1_CEN  = 1;             //使能计数器
}

void Init_Ad()
{
	AdPga = 8;
}   

//设置定时器——时间片个数,12.8ms/
void Set_Timer(uchar TimeIndex ,uint Count)
{
	TimerCount[TimeIndex] = Count;       //置延时单位时间个数
	if( Count ==0 )
		MessageFlag[TimeIndex] = true;     //立即置任务标志
}

//任务初始化
void Init_Task(void)
{                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     
	uchar i;
	
	Step_Ad = 0;                 //AD采样步骤复位
	for(i=0;i<COUNT_NUM;i++)
	{
		MessageFlag[i] = true;     //任务开
		TimerCount[i]=0xff;
	}
	
	SELAH;
	
	ChannelMax = 0;
}

//Initial AD model
void AdInit(uchar Channel)
{
  Ch[ 1 ].SignalInputType = Ch[ 0 ].SignalInputType;          //信号类型
  switch( Ch[ 0 ].SignalInputType )
  {
  	case IN_0_10mA:                                        
  	case IN_0_20mA:                                     
  	case IN_4_20mA:                                    
  	         AdPga = 4;	                           
  	         break;                                    

  	case IN_0_5V:                                      
  	case IN_1_5V:                                                                
  	case IN_0_10V:                                                                   
  	         AdPga = 8;                       
  	         break;
                              
  	default:                                                                
  	         AdPga = 4;                               
  	         break;
  }
  
  nop;
  nop;
}      

/**************************************
* 功能：+-1滤波，有符号整型
***************************************/
void lFilter(long *Data,long *Data_Bck)
{
	if( (*Data >(*Data_Bck+2) )||(*Data_Bck >(*Data+2)) )
		*Data_Bck  = *Data;
	else
		{
			if( *Data >(*Data_Bck+1) )
				(*Data_Bck)++;
			
			if(*Data_Bck >(*Data+1))
				(*Data_Bck)-- ;
		}
	*Data  = *Data_Bck;
}

long GetClaiValue_5du(long InData ,long InFull ,long InZero , long OutFull , long OutZero)
{
  long iii_u;
  
	//InZero = 0;
	iii_u = (InData - InZero);
	if(iii_u==0)
		return( OutZero );
	
	iii_u *= (OutFull - OutZero);
	iii_u /= (InFull - InZero);
	iii_u += OutZero;
	
  return( iii_u );
}

void InputCurrentPro(void)
{
	long PARA_H,PARA_L;
	long iii;
	
	Ch[ Channel ].InputStatu = 0;
	
	iii = Ch[ Channel ].AdValueA;

	PARA_H = Ch[ Channel ].InputCurrent;
	//PARA_L = Ch[ Channel ].InputCurrent_Zero;
	PARA_L = AdOffset;//Ch[ Channel ].InputCurrent_Zero;
				
	iii = GetClaiValue_5du(iii , PARA_H, PARA_L, mInputCurrentH, 0);
 	
	if( (Ch[ 0 ].SignalInputType==IN_4_20mA )||(Ch[ 0 ].SignalInputType==IN_1_5V) )
		{
			//4~20mA ,<0.1%(16uA)断线
			if(iii<3600)
				{
					Ch[ Channel ].InputStatu=0x5a;
					iii = 3500;
				}
		}

	if( iii<0)
		iii = 0;
	
	//送计算值
	Ch[ Channel ].InValueBuf = iii;
	
	//送显示值
	if( (Ch[ 0 ].SignalInputType==IN_1_5V)||( Ch[ 0 ].SignalInputType==IN_0_5V) )
		{
			Ch[ Channel ].InValue = iii>>2;
		}
	else if(Ch[ 0 ].SignalInputType==IN_0_10V)
		{
			Ch[ Channel ].InValue = iii>>1;
		}
	else
		{
			Ch[ Channel ].InValue = iii;
		}
 	
 	//送校准值
 	Ch[ Channel ].aa = Ch[ Channel ].InValue;
}
/*
//梯推平均滤波
long SumFilt(long DataIn,long OldData,long *p,uchar *Count)
{
	uchar i,j;
	long SUM;
	
	long temp;
	
	//先进后出
	p[ 0 ] = DataIn;
	for(i=(AVERAGE_TIME-1);i>0;i--)
	{
		p[i] = p[i-1];
	}
	
	(*Count)++;
	if( *Count >=AVERAGE_TIME )
		{
			*Count = 0;
			
			//冒泡排序
			for(i=0;i<AVERAGE_TIME;i++)
			{
				AdValue_PX[i] = p[i];
			}
			
			//5.5ms
			for(i=0;i<AVERAGE_TIME;i++)
			{
				for(j=i+1;j<AVERAGE_TIME;j++)
				{
					if( AdValue_PX[i] > AdValue_PX[j] )
						{
							temp = AdValue_PX[i];
							AdValue_PX[i] = AdValue_PX[j];
							AdValue_PX[j] = temp;
						}
				}
			}
			
			SUM = 0;
			//取4个大值
			//for(i=4 ;i<(AVERAGE_TIME);i++)
			//{
			//	SUM += AdValue_PX[i];
			//}
			//取中间8个
			for(i=2 ;i<(AVERAGE_TIME-2);i++)
			{
				SUM += AdValue_PX[i];
			}

			SUM /= AVERAGE_TIME_NUM;
		}
	else
		{
			SUM = OldData;
		}

	return(SUM);
}
*/

void InSignalDis(void)
{
	long  L,H;              //变送范围（或测量上下限）
		
	long  F;
  if( Ch[ Channel ].InputStatu==0x5a )
  {
  	Ch[ Channel ].AdProData = -4000;
  	return;
  }
 
 switch(Ch[ 0 ].SignalInputType)
  {
   case IN_0_10V:			//0-10V
   case IN_0_5V:			//0-5V (0-20000)
   case IN_0_20mA:    //0-20mA (0-20000)
   	         L = 0;
   	         H = 20000;
   	         	break;
   case IN_1_5V:			//1-5V (4000-20000)
   case IN_4_20mA:		//4-20mA (4000-20000)
   	         L = 4000;
   	         H = 20000;
             break;
   case IN_0_10mA:		//0-10mA (0-10000)
   	         L = 0;
   	         H = 10000;
             break;
 
   default:  
   	         L = 4000;
   	         H = 20000;
             break;
	}

	F  = (Ch[ Channel ].InValueBuf-L);
	F *= 16000;
	F += ( (H-L)>>1);
	F /= (H-L);
	//F +=5;
	//F /=10;
	
	iii = (long)F;
//------------------------------------- Narmal is 100%(0~16000),  Max = (-200% ~ +200%)
	//超量程判断（ <3.600mA or > 20.400mA ）,（ <下限2.5% or > 上限2.5% ）  取消超量程，2018.11.05
	if( iii<-4000 )
		{
			iii = -4000;
			//Ch.InputStatu =0xa5;
		}
	else if( iii>21000 )
		{
			iii = 21000;
			//Ch.InputStatu =0xa5;
		}
//-----------------------------------------------------				
  Ch[ Channel ].AdProData = iii;
}

//AD采集任务
void ReadAd_task(void)
{
		if(MessageFlag[ TIME_0 ] == false)             //任务未准备好 
			return;
		
		MessageFlag[ TIME_0 ] = false ;                //结束自己任务，等待新的触发

		switch( Step_Ad )
		{
			case 0:
			       AdInit( Channel );                     //类型初始化
			       
			       if(Channel==0)
			       	{
			       		SELAH;
			       	}
			       else
			       	{
			       		SELAL;
			       	}	       		
			       Delay(60);                             //延时3ms
			       
			       StartCs1237();                         //启动CS1237  PowerDown恢复
			       
		         Set_Timer(TIME_0,7);                   //7*8.192ms
		         
		         Step_Ad=1;
		         
		         break;

			case 1:
				     //PA_ODR_ODR3 = 1;
				     
				     //以下2.5ms
				     iii = ReadCs1237();
				     
				     //关CS1237，进入PowerDown
				     	StopCs1237();
				     	
				     //Ch.AdValueA = SumFilt( iii ,Ch.AdValueA ,Ch.AdValueABuf ,&Ch.AdValueACounter );
				     
				     Ch[ Channel ].AdValueA = iii;
				     
				     lFilter(&Ch[ Channel ].AdValueA,&Ch[ Channel ].AdValueA_Bck);

//----------------------------------------------------------------------------处理
					   InputCurrentPro();
					   
				     InSignalDis();                 //输入信号处理
				     
				     OutputCurrentPro();
				     
				     //PA_ODR_ODR3 = 0;
				     
				     if( ChannelMax == 0 )//选择通道
				     	{
				     		Channel = 0;                //单通道
				     	}
				     else
				     	{
				     		if( ++Channel >= 2 )        //双通道
				     			Channel = 0;
				     	}
				     
				     Set_Timer(TIME_0,0);
				     Step_Ad=0;
				     break;

		  default:
		  	     Set_Timer(TIME_0,0);
		  	     Step_Ad = 0;                  
		  	     break;
		}
}

//报警指示灯输出
//正    常：灭；
//开路闪烁：73*8.192ms = 600ms；
//短路闪烁：12*8.192ms = 100ms；
//超 量 程：常亮
void AlarmLedOut_task(void)
{
	if(MessageFlag[ TIME_1 ] == false)             //任务未准备好 
		return;
	
	MessageFlag[ TIME_1 ] = false ;                //结束自己任务，等待新的触发
	
	Set_Timer(TIME_1,55);
	
	if( FlagCursor )
		{
			FlagCursor = 0;
		}
	else
		{
			FlagCursor = 1;
		}
	
	if( (Ch[ 0 ].InputStatu==0x5a )||(Ch[ 0 ].InputStatu==0x5b) )
		{
			//闪烁
			if( FlagCursor )
				Ch1AlarmLed =1;
			else
				Ch1AlarmLed =0;
		}
	else if( Ch[ 0 ].InputStatu==0xa5 )
		{
			//常亮
			Ch1AlarmLed =0;
		}
	else
		{
			//灭
			Ch1AlarmLed =1;
		}
	
	//如果不是双通道
	if( ChannelMax ==0 )
		{
			//灭
			Ch2AlarmLed =1;
			return;
		}
	
	if( (Ch[ 1 ].InputStatu==0x5a )||(Ch[ 1 ].InputStatu==0x5b) )
		{
			//闪烁
			if( FlagCursor )
				Ch2AlarmLed =1;
			else
				Ch2AlarmLed =0;
		}
	else if( Ch[ 1 ].InputStatu==0xa5 )
		{
			//常亮
			Ch2AlarmLed =0;
		}
	else
		{
			//灭
			Ch2AlarmLed =1;
		}
}


//更新定时的时间片8.192ms内
void UpdateTimer(void) 
{
	uchar i;
	if (TIM2_SR1_UIF)
	{		
		TIM2_SR1_UIF = 0;
		//PA_ODR_ODR3 ^= 1;//测试位test
		nop;
		for(i=0;i<COUNT_NUM;i++)
		{
			if( TimerCount[i] ==0xff)   //定时器使能：0xff---关
				continue;
			if(TimerCount[i] !=0)
				{
					TimerCount[i]--;
					if(TimerCount[i] ==0)
						MessageFlag[i] = true;     //置任务标志	
				}
		}

//编程器数据50ms超时		
		if(recv_TimeOut == 0)
			{
				recv_state = FSA_INIT;         //Return Recv State
			}
		else
			{
				recv_TimeOut--; 	
			}

//通讯1.3S超时，FlagCom = 156，TXD/SWIM 作故障指示灯控制
		if(FlagCom != 0)
			{
				FlagCom--; 	
			}
	}
}

void main( void )
{
  Init_Clock();
  
  Init_Io();
  
  Delay(50);// 5mS
  
  Init_TIM1();

  Init_TIM2();
  
  //Init_Ad();
  
  Init_Uart();
  
  Init_Flash();
  
  Init_Cs1237( CONFIG_REF_OFF | CONFIG_SPEED_40HZ | CONFIG_PGA_2 | CONFIG_CHSEL_SHAORT );

  StartCs1237();
  		
  AdOffset = ReadCs1237();
  		
  SetConfig_Cs1237( CONFIG_REF_OFF | CONFIG_SPEED_40HZ | CONFIG_PGA_2 | CONFIG_CHSEL_A );
  
  StopCs1237();
  
  Delay(100);// 10mS

  Delay(100);// 10mS

  Init_Task();
  
  Init_IWDG();

  EINT;
  
  while(1)
  {
    ClearWdt;
    
    //PD_ODR_ODR0 = 1;
    
    UpdateTimer();
    nop;
    nop;
    
    ReadAd_task();
    nop;
    nop;
    
    AlarmLedOut_task();
    nop;
    nop;
    
    LinUart_Exe();
    nop;
    nop;
    //PA_ODR_ODR3 = 1;
    
    Delay(10);
    
    //PA_ODR_ODR3 = 0;
    
    
  }
  
}
