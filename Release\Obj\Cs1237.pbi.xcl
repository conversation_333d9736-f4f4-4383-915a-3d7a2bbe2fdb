"D:\Src ST\STM8S003F3（mA 2In 2Out）\Src\Cs1237.c"
-std=c99
-ferror-limit=0
-I
D:\Program Files (x86)\IAR Systems\Embedded Workbench 8.0\stm8\inc
-I
D:\Program Files (x86)\IAR Systems\Embedded Workbench 8.0\stm8\inc\c
-D__CHAR_BITS__=8
-D__CHAR_MAX__=0xff
-D__CHAR_MIN__=0
-D__CHAR_SIZE__=1
-D__UNSIGNED_CHAR_MAX__=0xff
-D__SIGNED_CHAR_MAX__=127
-D__SIGNED_CHAR_MIN__=(-__SIGNED_CHAR_MAX__-1)
-D__CHAR_ALIGN__=1
-D__SHORT_SIZE__=2
-D__UNSIGNED_SHORT_MAX__=0xffffU
-D__SIGNED_SHORT_MAX__=32767
-D__SIGNED_SHORT_MIN__=(-__SIGNED_SHORT_MAX__-1)
-D__SHORT_ALIGN__=1
-D__INT_SIZE__=2
-D__UNSIGNED_INT_MAX__=0xffffU
-D__SIGNED_INT_MAX__=32767
-D__SIGNED_INT_MIN__=(-__SIGNED_INT_MAX__-1)
-D__INT_ALIGN__=1
-D__LONG_SIZE__=4
-D__UNSIGNED_LONG_MAX__=0xffffffffUL
-D__SIGNED_LONG_MAX__=2147483647L
-D__SIGNED_LONG_MIN__=(-__SIGNED_LONG_MAX__-1)
-D__LONG_ALIGN__=1
-D__LONG_LONG_SIZE__=4
-D__UNSIGNED_LONG_LONG_MAX__=0xffffffffULL
-D__SIGNED_LONG_LONG_MAX__=2147483647LL
-D__SIGNED_LONG_LONG_MIN__=(-__SIGNED_LONG_LONG_MAX__-1)
-D__LONG_LONG_ALIGN__=1
-D__INT8_T_TYPE__=signed char
-D__INT8_T_MAX__=127
-D__INT8_T_MIN__=(-__INT8_T_MAX__-1)
-D__UINT8_T_TYPE__=unsigned char
-D__UINT8_T_MAX__=0xff
-D__INT8_SIZE_PREFIX__="hh"
-D__INT16_T_TYPE__=signed int
-D__INT16_T_MAX__=32767
-D__INT16_T_MIN__=(-__INT16_T_MAX__-1)
-D__UINT16_T_TYPE__=unsigned int
-D__UINT16_T_MAX__=0xffffU
-D__INT16_SIZE_PREFIX__=""
-D__INT32_T_TYPE__=signed long int
-D__INT32_T_MAX__=2147483647L
-D__INT32_T_MIN__=(-__INT32_T_MAX__-1)
-D__UINT32_T_TYPE__=unsigned long int
-D__UINT32_T_MAX__=0xffffffffUL
-D__INT32_SIZE_PREFIX__="l"
-D__INT_LEAST8_T_TYPE__=signed char
-D__INT_LEAST8_T_MAX__=127
-D__INT_LEAST8_T_MIN__=(-__INT_LEAST8_T_MAX__-1)
-D__UINT_LEAST8_T_TYPE__=unsigned char
-D__UINT_LEAST8_T_MAX__=0xff
-D__INT8_C_SUFFIX__=
-D__UINT8_C_SUFFIX__=
-D__INT_LEAST8_SIZE_PREFIX__="hh"
-D__INT_LEAST16_T_TYPE__=signed int
-D__INT_LEAST16_T_MAX__=32767
-D__INT_LEAST16_T_MIN__=(-__INT_LEAST16_T_MAX__-1)
-D__UINT_LEAST16_T_TYPE__=unsigned int
-D__UINT_LEAST16_T_MAX__=0xffffU
-D__INT16_C_SUFFIX__=
-D__UINT16_C_SUFFIX__=U
-D__INT_LEAST16_SIZE_PREFIX__=""
-D__INT_LEAST32_T_TYPE__=signed long int
-D__INT_LEAST32_T_MAX__=2147483647L
-D__INT_LEAST32_T_MIN__=(-__INT_LEAST32_T_MAX__-1)
-D__UINT_LEAST32_T_TYPE__=unsigned long int
-D__UINT_LEAST32_T_MAX__=0xffffffffUL
-D__INT32_C_SUFFIX__=L
-D__UINT32_C_SUFFIX__=UL
-D__INT_LEAST32_SIZE_PREFIX__="l"
-D__INT_FAST8_T_TYPE__=signed char
-D__INT_FAST8_T_MAX__=127
-D__INT_FAST8_T_MIN__=(-__INT_FAST8_T_MAX__-1)
-D__UINT_FAST8_T_TYPE__=unsigned char
-D__UINT_FAST8_T_MAX__=0xff
-D__INT_FAST8_SIZE_PREFIX__="hh"
-D__INT_FAST16_T_TYPE__=signed int
-D__INT_FAST16_T_MAX__=32767
-D__INT_FAST16_T_MIN__=(-__INT_FAST16_T_MAX__-1)
-D__UINT_FAST16_T_TYPE__=unsigned int
-D__UINT_FAST16_T_MAX__=0xffffU
-D__INT_FAST16_SIZE_PREFIX__=""
-D__INT_FAST32_T_TYPE__=signed long int
-D__INT_FAST32_T_MAX__=2147483647L
-D__INT_FAST32_T_MIN__=(-__INT_FAST32_T_MAX__-1)
-D__UINT_FAST32_T_TYPE__=unsigned long int
-D__UINT_FAST32_T_MAX__=0xffffffffUL
-D__INT_FAST32_SIZE_PREFIX__="l"
-D__INTMAX_T_TYPE__=signed long int
-D__INTMAX_T_MAX__=2147483647L
-D__INTMAX_T_MIN__=(-__INTMAX_T_MAX__-1)
-D__UINTMAX_T_TYPE__=unsigned long int
-D__UINTMAX_T_MAX__=0xffffffffUL
-D__INTMAX_C_SUFFIX__=L
-D__UINTMAX_C_SUFFIX__=UL
-D__INTMAX_SIZE_PREFIX__="l"
-D__FLOAT_SIZE__=4
-D__FLOAT_ALIGN__=1
-D__DOUBLE_SIZE__=4
-D__DOUBLE_ALIGN__=1
-D__LONG_DOUBLE_SIZE__=4
-D__LONG_DOUBLE_ALIGN__=1
-D__NAN_HAS_HIGH_MANTISSA_BIT_SET__=0
-D__SUBNORMAL_FLOATING_POINTS__=1
-D__SIZE_T_TYPE__=unsigned short int
-D__SIZE_T_MAX__=0xffffU
-D__PTRDIFF_T_TYPE__=signed short int
-D__PTRDIFF_T_MAX__=32767
-D__PTRDIFF_T_MIN__=(-__PTRDIFF_T_MAX__-1)
-D__INTPTR_T_TYPE__=signed short int
-D__INTPTR_T_MAX__=32767
-D__INTPTR_T_MIN__=(-__INTPTR_T_MAX__-1)
-D__UINTPTR_T_TYPE__=unsigned short int
-D__UINTPTR_T_MAX__=0xffffU
-D__INTPTR_SIZE_PREFIX__="h"
-D__JMP_BUF_ELEMENT_TYPE__=unsigned char
-D__JMP_BUF_NUM_ELEMENTS__=28
-D__TID__=0x3800
-D__VER__=310
-D__SUBVERSION__=1
-D__BUILD_NUMBER__=201
-D__IAR_SYSTEMS_ICC__=8
-D__VA_STACK_DECREASING__=1
-D__VA_STACK_ALIGN__=1
-D__VA_STACK_ALIGN_EXTRA_BEFORE__=1
-D__LITTLE_ENDIAN__=0
-D__BOOL_TYPE__=unsigned char
-D__BOOL_SIZE__=1
-D__WCHAR_T_TYPE__=unsigned short int
-D__WCHAR_T_SIZE__=2
-D__WCHAR_T_MAX__=0xffffU
-D__DEF_PTR_MEM__=__near
-D__DEF_PTR_SIZE__=2
-D__CODE_MEMORY_LIST3__(_P1,_P2)=__CODE_MEM_HELPER3__(__near_func, 0, _P1, _P2)
-D__DATA_MEMORY_LIST3__(_P1,_P2)=__DATA_MEM_HELPER3__(__tiny, 0, _P1, _P2) __DATA_MEM_HELPER3__(__near, 1, _P1, _P2) __DATA_MEM_HELPER3__(__far, 2, _P1, _P2) __DATA_MEM_HELPER3__(__huge, 3, _P1, _P2) __DATA_MEM_HELPER3__(__eeprom, 4, _P1, _P2)
-D__CODE_MEM0__=__near_func
-D__CODE_MEM0_POINTER_OK__=1
-D__CODE_MEM0_UNIQUE_POINTER__=1
-D__CODE_MEM0_VAR_OK__=1
-D__DATA_MEM0__=__tiny
-D__DATA_MEM0_POINTER_OK__=1
-D__DATA_MEM0_UNIQUE_POINTER__=1
-D__DATA_MEM0_VAR_OK__=1
-D__DATA_MEM0_INTPTR_TYPE__=signed char
-D__DATA_MEM0_UINTPTR_TYPE__=unsigned char
-D__DATA_MEM0_INTPTR_SIZE_PREFIX__="hh"
-D__DATA_MEM0_MAX_SIZE__=0xff
-D__DATA_MEM1__=__near
-D__DATA_MEM1_POINTER_OK__=1
-D__DATA_MEM1_UNIQUE_POINTER__=1
-D__DATA_MEM1_VAR_OK__=1
-D__DATA_MEM1_INDEX_TYPE__=short
-D__DATA_MEM1_SIZE_TYPE__=unsigned short
-D__DATA_MEM1_INTPTR_TYPE__=short int
-D__DATA_MEM1_UINTPTR_TYPE__=unsigned short int
-D__DATA_MEM1_INTPTR_SIZE_PREFIX__="h"
-D__DATA_MEM1_MAX_SIZE__=0xffff
-D__DATA_MEM1_HEAP_SEGMENT__="HEAP"
-D__DATA_MEM1_PAGE_SIZE__=0
-D__DATA_MEM1_HEAP__=1
-D__DATA_MEM2__=__far
-D__DATA_MEM2_POINTER_OK__=1
-D__DATA_MEM2_UNIQUE_POINTER__=1
-D__DATA_MEM2_VAR_OK__=1
-D__DATA_MEM2_INDEX_TYPE__=short
-D__DATA_MEM2_SIZE_TYPE__=unsigned short
-D__DATA_MEM2_INTPTR_TYPE__=long int
-D__DATA_MEM2_UINTPTR_TYPE__=unsigned long int
-D__DATA_MEM2_INTPTR_SIZE_PREFIX__="l"
-D__DATA_MEM2_MAX_SIZE__=0xffff
-D__DATA_MEM3__=__huge
-D__DATA_MEM3_POINTER_OK__=1
-D__DATA_MEM3_UNIQUE_POINTER__=1
-D__DATA_MEM3_VAR_OK__=1
-D__DATA_MEM3_INDEX_TYPE__=long
-D__DATA_MEM3_SIZE_TYPE__=unsigned long
-D__DATA_MEM3_INTPTR_TYPE__=long int
-D__DATA_MEM3_UINTPTR_TYPE__=unsigned long int
-D__DATA_MEM3_INTPTR_SIZE_PREFIX__="l"
-D__DATA_MEM3_MAX_SIZE__=0xffffffff
-D__DATA_MEM4__=__eeprom
-D__DATA_MEM4_POINTER_OK__=1
-D__DATA_MEM4_UNIQUE_POINTER__=1
-D__DATA_MEM4_VAR_OK__=1
-D__DATA_MEM4_INDEX_TYPE__=short
-D__DATA_MEM4_SIZE_TYPE__=unsigned short
-D__DATA_MEM4_INTPTR_TYPE__=short int
-D__DATA_MEM4_UINTPTR_TYPE__=unsigned short int
-D__DATA_MEM4_INTPTR_SIZE_PREFIX__="h"
-D__DATA_MEM4_MAX_SIZE__=0xffff
-D__CODE_PTR_MEMORY_LIST3__(_P1,_P2)=__CODE_PTR_MEM_HELPER3__(__near_func, 0, _P1, _P2)
-D__DATA_PTR_MEMORY_LIST3__(_P1,_P2)=__DATA_PTR_MEM_HELPER3__(__tiny, 0, _P1, _P2) __DATA_PTR_MEM_HELPER3__(__near, 1, _P1, _P2) __DATA_PTR_MEM_HELPER3__(__far, 2, _P1, _P2) __DATA_PTR_MEM_HELPER3__(__huge, 3, _P1, _P2) __DATA_PTR_MEM_HELPER3__(__eeprom, 4, _P1, _P2)
-D__VAR_MEMORY_LIST3__(_P1,_P2)=__VAR_MEM_HELPER3__(__tiny, 0, _P1, _P2) __VAR_MEM_HELPER3__(__near, 1, _P1, _P2) __VAR_MEM_HELPER3__(__far, 2, _P1, _P2) __VAR_MEM_HELPER3__(__huge, 3, _P1, _P2) __VAR_MEM_HELPER3__(__eeprom, 4, _P1, _P2)
-D__HEAP_MEM0__=1
-D__HEAP_DEFAULT_MEM__=1
-D__HEAP_MEMORY_LIST3__(_P1,_P2)=__HEAP_MEM_HELPER3__(__near, 1, _P1, _P2)
-D__MULTIPLE_HEAPS__=0
-D__TOPM_DATA_MEMORY_LIST3__(_P1,_P2)=__TOPM_DATA_MEM_HELPER3__(__huge, 3, _P1, _P2) __TOPM_DATA_MEM_HELPER3__(__eeprom, 4, _P1, _P2)
-D__TOPP_DATA_MEMORY_LIST3__(_P1,_P2)=__TOPP_DATA_MEM_HELPER3__(__near, 1, _P1, _P2) __TOPP_DATA_MEM_HELPER3__(__huge, 3, _P1, _P2) __TOPP_DATA_MEM_HELPER3__(__eeprom, 4, _P1, _P2)
-D__DEF_HEAP_MEM__=__near
-D__MULTIPLE_INHERITANCE__=1
-D_RTSL_COMPARE_T=unsigned char
-D__CODE_MODEL__=__SMALL_CODE_MODEL__
-D__CORE__=__STM8__
-D__DATA_MODEL__=__MEDIUM_DATA_MODEL__
-D__ICCSTM8__=1
-D__LARGE_CODE_MODEL__=3
-D__LARGE_DATA_MODEL__=3
-D__MEDIUM_CODE_MODEL__=2
-D__MEDIUM_DATA_MODEL__=2
-D__SMALL_CODE_MODEL__=1
-D__SMALL_DATA_MODEL__=1
-D__STM8__=1
-D__PLAIN_INT_BITFIELD_IS_SIGNED__=1
-D__HAS_WEAK__=1
-D__HAS_LOCATED_DECLARATION__=1
-D__HAS_LOCATED_WITH_INIT__=1
-D__IAR_COMPILERBASE__=595714
-D__STDC__=1
-D__STDC_VERSION__=199901L
-D__STDC_HOSTED__=1
-D__STDC_NO_VLA__=1
-D__STDC_NO_ATOMICS__=1
-D__EDG_IA64_ABI=1
-D__EDG_IA64_ABI_VARIANT_CTORS_AND_DTORS_RETURN_THIS=1
-D__EDG_IA64_ABI_USE_INT_STATIC_INIT_GUARD=1
-D__EDG_TYPE_TRAITS_ENABLED=1
-D__EDG__=1
-D__EDG_VERSION__=410
-D__EDG_SIZE_TYPE__=unsigned short
-D__EDG_PTRDIFF_TYPE__=short
-D__EDG_DELTA_TYPE=short
-D__EDG_IA64_VTABLE_ENTRY_TYPE=short
-D__EDG_VIRTUAL_FUNCTION_INDEX_TYPE=unsigned short
-D__EDG_LOWER_VARIABLE_LENGTH_ARRAYS=1
-D__EDG_IA64_ABI_USE_VARIANT_ARRAY_COOKIES=1
-D__EDG_ABI_COMPATIBILITY_VERSION=9999
-D__EDG_ABI_CHANGES_FOR_RTTI=1
-D__EDG_ABI_CHANGES_FOR_ARRAY_NEW_AND_DELETE=1
-D__EDG_ABI_CHANGES_FOR_PLACEMENT_DELETE=1
-D__EDG_BSD=0
-D__EDG_SYSV=0
-D__EDG_ANSIC=1
-D__EDG_CPP11_IL_EXTENSIONS_SUPPORTED=1
-DNDEBUG=1
-D_DLIB_CONFIG_FILE_HEADER_NAME="D:\Program Files (x86)\IAR Systems\Embedded Workbench 8.0\stm8\LIB\dlstm8smn.h"
-D_DLIB_CONFIG_FILE_STRING="D:\\Program Files (x86)\\IAR Systems\\Embedded Workbench 8.0\\stm8\\LIB\\dlstm8smn.h"
-D__VERSION__="IAR C/C++ Compiler V3.10.1.201 for STM8"
-D__CODE_MEMORY_LIST1__()=__CODE_MEM_HELPER1__(__code, 0 )
-D__CODE_MEMORY_LIST2__(_P1)=__CODE_MEM_HELPER2__(__code, 0 ,  _P1 )
-D__CODE_MEMORY_LIST3__(_P1, _P2)=__CODE_MEM_HELPER3__(__code, 0 ,  _P1 ,  _P2 )
-D__DATA_MEMORY_LIST1__()=__DATA_MEM_HELPER1__(__data, 0 )
-D__DATA_MEMORY_LIST2__(_P1)=__DATA_MEM_HELPER2__(__data, 0 ,  _P1 )
-D__DATA_MEMORY_LIST3__(_P1, _P2)=__DATA_MEM_HELPER3__(__data, 0 ,  _P1 ,  _P2 )
-D__CODE_PTR_MEMORY_LIST1__()=__CODE_PTR_MEM_HELPER1__(__code, 0 )
-D__CODE_PTR_MEMORY_LIST2__(_P1)=__CODE_PTR_MEM_HELPER2__(__code, 0 ,  _P1 )
-D__CODE_PTR_MEMORY_LIST3__(_P1, _P2)=__CODE_PTR_MEM_HELPER3__(__code, 0 ,  _P1 ,  _P2 )
-D__DATA_PTR_MEMORY_LIST1__()=__DATA_PTR_MEM_HELPER1__(__data, 0 )
-D__DATA_PTR_MEMORY_LIST2__(_P1)=__DATA_PTR_MEM_HELPER2__(__data, 0 ,  _P1 )
-D__DATA_PTR_MEMORY_LIST3__(_P1, _P2)=__DATA_PTR_MEM_HELPER3__(__data, 0 ,  _P1 ,  _P2 )
-D__VAR_MEMORY_LIST1__()=__VAR_MEM_HELPER1__(__data, 0 )
-D__VAR_MEMORY_LIST2__(_P1)=__VAR_MEM_HELPER2__(__data, 0 ,  _P1 )
-D__VAR_MEMORY_LIST3__(_P1, _P2)=__VAR_MEM_HELPER3__(__data, 0 ,  _P1 ,  _P2 )
-D__VARD_MEMORY_LIST1__()=__VARD_MEM_HELPER1__(__data, 0, _ )
-D__HEAP_MEMORY_LIST1__()=__HEAP_MEM_HELPER1__(__data, 0 )
-D__HEAP_MEMORY_LIST2__(_P1)=__HEAP_MEM_HELPER2__(__data, 0 ,  _P1 )
-D__HEAP_MEMORY_LIST3__(_P1, _P2)=__HEAP_MEM_HELPER3__(__data, 0 ,  _P1 ,  _P2 )
-D__HVAR_MEMORY_LIST1__()=__HVAR_MEM_HELPER1__(__data, 0 )
-D__HEAPD_MEMORY_LIST1__()=__HEAPD_MEM_HELPER1__(__data, 0, _ )
-D__HEAPU_MEMORY_LIST1__()=__HEAPU_MEM_HELPER1__(__data, 0 )
-D__TOPM_DATA_MEMORY_LIST1__()=
-D__TOPM_DATA_MEMORY_LIST2__(_P1)=
-D__TOPM_DATA_MEMORY_LIST3__(_P1, _P2)=
-D__TOPP_DATA_MEMORY_LIST1__()=__TOPP_DATA_MEM_HELPER1__(__data, 0 )
-D__TOPP_DATA_MEMORY_LIST2__(_P1)=__TOPP_DATA_MEM_HELPER2__(__data, 0 ,  _P1 )
-D__TOPP_DATA_MEMORY_LIST3__(_P1, _P2)=__TOPP_DATA_MEM_HELPER3__(__data, 0 ,  _P1 ,  _P2 )
-D__DATA_MEM0_SIZE_TYPE__=unsigned int
-D__DATA_MEM0_INDEX_TYPE__=signed int
