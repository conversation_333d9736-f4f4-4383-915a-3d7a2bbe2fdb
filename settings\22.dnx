<?xml version="1.0"?>
<settings>
    <Stack>
        <FillEnabled>0</FillEnabled>
        <OverflowWarningsEnabled>1</OverflowWarningsEnabled>
        <WarningThreshold>90</WarningThreshold>
        <SpWarningsEnabled>1</SpWarningsEnabled>
        <WarnLogOnly>1</WarnLogOnly>
        <UseTrigger>1</UseTrigger>
        <TriggerName>main</TriggerName>
        <LimitSize>0</LimitSize>
        <ByteLimit>50</ByteLimit>
    </Stack>
    <Trace1>
        <Enabled>0</Enabled>
        <ShowSource>1</ShowSource>
    </Trace1>
    <DebugChecksum>
        <Checksum>1566863966</Checksum>
    </DebugChecksum>
    <CodeCoverage>
        <Enabled>_ 0</Enabled>
    </CodeCoverage>
    <Disassembly>
        <InstrCount>0</InstrCount>
        <MixedMode>1</MixedMode>
    </Disassembly>
    <CallStack>
        <ShowArgs>0</ShowArgs>
    </CallStack>
    <SfrWindow>
        <Show>1 1</Show>
        <Sort>4 0</Sort>
    </SfrWindow>
    <BreakpointUsageDialog>
        <Placement>_ 357 278 802 560</Placement>
    </BreakpointUsageDialog>
    <DriverProfiling>
        <Enabled>0</Enabled>
        <Mode>1</Mode>
        <Graph>0</Graph>
        <Symbiont>0</Symbiont>
        <Exclusions />
    </DriverProfiling>
    <CallStackLog>
        <Enabled>0</Enabled>
    </CallStackLog>
    <CallStackStripe>
        <ShowTiming>1</ShowTiming>
    </CallStackStripe>
    <InterruptLog>
        <LogEnabled>0</LogEnabled>
        <GraphEnabled>0</GraphEnabled>
        <ShowTimeLog>1</ShowTimeLog>
        <SumEnabled>0</SumEnabled>
        <ShowTimeSum>1</ShowTimeSum>
        <SumSortOrder>0</SumSortOrder>
    </InterruptLog>
    <DataLog>
        <LogEnabled>0</LogEnabled>
        <GraphEnabled>0</GraphEnabled>
        <ShowTimeLog>1</ShowTimeLog>
        <SumEnabled>0</SumEnabled>
        <ShowTimeSum>1</ShowTimeSum>
    </DataLog>
    <Breakpoints2>
        <Bp0>_ 1 "STD_CODE2" "{$PROJ_DIR$\Src\main.c}.593.3" 0 0 1 "" 0 ""</Bp0>
        <Count>1</Count>
    </Breakpoints2>
    <Interrupts>
        <Enabled>1</Enabled>
    </Interrupts>
    <MemConfig>
        <Base>1</Base>
        <Manual>0</Manual>
        <Ddf>1</Ddf>
        <TypeViol>0</TypeViol>
        <Stop>1</Stop>
    </MemConfig>
    <Simulator>
        <Freq>16000000</Freq>
        <FreqHi>0</FreqHi>
        <MultiCoreRunAll>1</MultiCoreRunAll>
    </Simulator>
    <watch_formats>
        <Fmt0>{W}1:CLK_CKDIVR	4	0</Fmt0>
        <Fmt1>{W}1:FLASH_IAPSR	4	0</Fmt1>
        <Fmt2>{W}1:PA_DDR	1	0</Fmt2>
        <Fmt3>{W}1:PA_ODR	1	0</Fmt3>
        <Fmt4>{W}1:PB_ODR	1	0</Fmt4>
        <Fmt5>{W}1:PC_CR1	1	0</Fmt5>
        <Fmt6>{W}1:PC_CR2	1	0</Fmt6>
        <Fmt7>{W}1:PC_DDR	1	0</Fmt7>
        <Fmt8>{W}1:PC_ODR	1	0</Fmt8>
        <Fmt9>{W}1:TIM3_CCMR2_COMPARE	4	0</Fmt9>
        <Fmt10>{W}1:USART_CR2	1	0</Fmt10>
        <Fmt11>{W}1:USART_SR	1	0</Fmt11>
    </watch_formats>
    <DataSample>
        <LogEnabled>0</LogEnabled>
        <GraphEnabled>0</GraphEnabled>
        <ShowTimeLog>1</ShowTimeLog>
    </DataSample>
    <LogFile>
        <LoggingEnabled>_ 0</LoggingEnabled>
        <LogFile>_ ""</LogFile>
        <Category>_ 0</Category>
    </LogFile>
    <TermIOLog>
        <LoggingEnabled>_ 0</LoggingEnabled>
        <LogFile>_ ""</LogFile>
    </TermIOLog>
    <Breakpoints>
        <Count>0</Count>
    </Breakpoints>
    <Aliases>
        <A0>_ "D:\2 ST\STM8S003F3（mA 2In 2Out）\Src\main.c" ""</A0>
        <Count>1</Count>
        <SuppressDialog>1</SuppressDialog>
    </Aliases>
    <DebuggerSettings>
        <DisableInterruptsWhenStepping>0</DisableInterruptsWhenStepping>
        <LeaveTargetRunning>0</LeaveTargetRunning>
    </DebuggerSettings>
</settings>
