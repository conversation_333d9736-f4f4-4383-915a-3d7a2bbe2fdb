param([String]$debugfile = "");

# This powershell file has been generated by the IAR Embedded Workbench
# C - SPY Debugger, as an aid to preparing a command line for running
# the cspybat command line utility using the appropriate settings.
#
# Note that this file is generated every time a new debug session
# is initialized, so you may want to move or rename the file before
# making changes.
#
# You can launch cspybat by typing Powershell.exe -File followed by the name of this batch file, followed
# by the name of the debug file (usually an ELF / DWARF or UBROF file).
#
# Read about available command line parameters in the C - SPY Debugging
# Guide. Hints about additional command line parameters that may be
# useful in specific cases :
#   --download_only   Downloads a code image without starting a debug
#                     session afterwards.
#   --silent          Omits the sign - on message.
#   --timeout         Limits the maximum allowed execution time.
#


if ($debugfile -eq "")
{
& "D:\ST-LIN V2\common\bin\cspybat" -f "E:\产品程序\Hex（STM8S003F3）\两入四出隔离器\STM8S003F3（mA 2In 2Out）\settings\22.Release.general.xcl" --backend -f "E:\产品程序\Hex（STM8S003F3）\两入四出隔离器\STM8S003F3（mA 2In 2Out）\settings\22.Release.driver.xcl" 
}
else
{
& "D:\ST-LIN V2\common\bin\cspybat" -f "E:\产品程序\Hex（STM8S003F3）\两入四出隔离器\STM8S003F3（mA 2In 2Out）\settings\22.Release.general.xcl" --debug_file=$debugfile --backend -f "E:\产品程序\Hex（STM8S003F3）\两入四出隔离器\STM8S003F3（mA 2In 2Out）\settings\22.Release.driver.xcl" 
}
