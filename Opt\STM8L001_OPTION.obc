DATASIZE_bit0:0
DATASIZE_bit1:0
DATASIZE_bit2:0
DATASIZE_bit3:0
DATASIZE_bit4:0
DATASIZE_bit5:0
I2C:Frozen when stopped
IWDG_HALT:Running
IWDG_HW:Software
ROP:Read-out protection disabled
Timer2:Frozen when stopped
Timer3:Running
Timer4:Frozen when stopped
UBC_bit0:0
UBC_bit1:0
UBC_bit2:0
UBC_bit3:0
UBC_bit4:0
UBC_bit5:0
UBC_bit6:0




#######################################################################################
#
# List of available option byte settings:
# =======================================
#
# 
# DATASIZE_bit0:0
# DATASIZE_bit0:1
# 
# DATASIZE_bit1:0
# DATASIZE_bit1:1
# 
# DATASIZE_bit2:0
# DATASIZE_bit2:1
# 
# DATASIZE_bit3:0
# DATASIZE_bit3:1
# 
# DATASIZE_bit4:0
# DATASIZE_bit4:1
# 
# DATASIZE_bit5:0
# DATASIZE_bit5:1
# 
# I2C:Frozen when stopped
# I2C:Running
# 
# IWDG_HALT:Running
# IWDG_HALT:Stopped
# 
# IWDG_HW:Software
# IWDG_HW:Hardware
# 
# ROP:Read-out protection enabled
# ROP:Read-out protection disabled
# 
# Timer2:Frozen when stopped
# Timer2:Running
# 
# Timer3:Frozen when stopped
# Timer3:Running
# 
# Timer4:Frozen when stopped
# Timer4:Running
# 
# UBC_bit0:0
# UBC_bit0:1
# 
# UBC_bit1:0
# UBC_bit1:1
# 
# UBC_bit2:0
# UBC_bit2:1
# 
# UBC_bit3:0
# UBC_bit3:1
# 
# UBC_bit4:0
# UBC_bit4:1
# 
# UBC_bit5:0
# UBC_bit5:1
# 
# UBC_bit6:0
# UBC_bit6:1
